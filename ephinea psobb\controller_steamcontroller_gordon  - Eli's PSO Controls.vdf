"controller_mappings"
{
	"version"		"3"
	"revision"		"328"
	"title"		"#Title"
	"description"		"#SettingsController_AutosaveDescription"
	"creator"		"76561198314857930"
	"progenitor"		"default://ephinea psobb"
	"url"		"autosave://C:\\Steam\\steamapps\\common\\Steam Controller Configs\\354592202\\config\\ephinea psobb\\controller_steamcontroller_gordon - Eli's PSO Controls.vdf"
	"export_type"		"unknown"
	"controller_type"		"controller_steamcontroller_gordon"
	"controller_caps"		"2179061"
	"major_revision"		"0"
	"minor_revision"		"0"
	"Timestamp"		"0"
	"actions"
	{
		"Default"
		{
			"title"		"Default"
			"legacy_set"		"1"
		}
	}
	"action_layers"
	{
		"Preset_1000001"
		{
			"title"		"While Left Trigger"
			"legacy_set"		"1"
			"set_layer"		"1"
			"parent_set_name"		"Default"
		}
		"Preset_1000002"
		{
			"title"		"While Right Trigger"
			"legacy_set"		"1"
			"set_layer"		"1"
			"parent_set_name"		"Default"
		}
		"Preset_1000003"
		{
			"title"		"While Right Grip"
			"legacy_set"		"1"
			"set_layer"		"1"
			"parent_set_name"		"Default"
		}
		"Preset_1000004"
		{
			"title"		"While Left Grip"
			"legacy_set"		"1"
			"set_layer"		"1"
			"parent_set_name"		"Default"
		}
	}
	"localization"
	{
		"english"
		{
			"title"		"Eli's PSO Controls"
			"description"		"Steam Input Sucks"
		}
		"czech"
		{
			"title"		"Gamepad s ovládáním kamery"
			"description"		"Tato šablona je pro většinu her podporujících gamepad a disponujících kamerou z pohledu první nebo třetí osoby. Mezi takové hry patří například akční hry z pohledu první nebo třetí osoby."
		}
		"danish"
		{
			"title"		"Gamepad med kamerastyring"
			"description"		"Denne skabelon er til de fleste spil, der allerede har indbygget gamepad-understøttelse og har et første- eller tredjepersonskontrolleret kamera. FPS eller tredjepersons adventure-spil osv."
		}
		"dutch"
		{
			"title"		"Gamepad met camerabesturing"
			"description"		"Deze template is voor de meeste spellen die reeds ingebouwde gamepadondersteuning hebben en die een camera hebben die wordt bestuurd in de eerste of derde persoon. FPS, third person-avontuurspellen, etc."
		}
		"finnish"
		{
			"title"		"Kameraa ohjaava peliohjain"
			"description"		"Tämä malli on useimmille muita ohjaimia valmiiksi tukeville peleille, joissa on ensimmäisessä tai kolmannessa persoonassa ohjattava kamera. FPS-pelit, kolmannen persoonan seikkailupelit jne."
		}
		"french"
		{
			"title"		"Manette avec contrôles caméra"
			"description"		"Ce modèle fonctionne pour la plupart des jeux ayant un support manette intégré et une caméra contrôlée à la première ou à la troisième personne. FPS, jeux d'aventure à la troisième personne, etc."
		}
		"german"
		{
			"title"		"Gamepad mit Kamerasteuerung"
			"description"		"Diese Vorlage ist für die meisten Spiele konzipiert, die bereits volle Untersützung für Gamepads mit sich bringen und eine First- oder Third-Person-Kamerasteuerung haben. Gedacht für Ego-Shooter, Third-Person-Abenteuerspiele usw."
		}
		"hungarian"
		{
			"title"		"Gamepad kamerairányítással"
			"description"		"Ez a sablon a legtöbb olyan játékhoz való, melyek már rendelkeznek beépített gamepad-támogatással, és van első vagy harmadik személyű kezelésű kamerájuk. Ilyenek az FPS vagy harmadik személyű kalandjátékok stb."
		}
		"italian"
		{
			"title"		"Gamepad con controlli della telecamera"
			"description"		"Questo template è pensato per la maggior parte dei giochi che hanno già il supporto per gamepad integrato e hanno la visuale controllata in prima o terza persona. Giochi d'avventura in terza persona, FPS ecc."
		}
		"japanese"
		{
			"title"		"カメラコントロール機能を持つゲームパッド"
			"description"		"FPS や、アドベンチャーゲームのような、一人称または三人称のカメラ操作を行うゲームパッドに標準対応したゲーム用のテンプレートです。"
		}
		"koreana"
		{
			"title"		"카메라 조작 기능이 있는 게임패드"
			"description"		"이 템플릿은 이미 게임패드 지원이 내장되어 있으며 1인칭 또는 3인칭 시점 카메라 조작을 지원하는 대부분의 게임을 위한 것입니다. FPS, 3인칭 어드벤쳐 게임 및 기타."
		}
		"polish"
		{
			"title"		"Kontroler obsługujący kamerę"
			"description"		"Ten szablon jest dla większości gier, które mają wbudowane wsparcie dla kontrolerów, a także kamerę kontrolowaną z perspektywy pierwszej lub trzeciej osoby, np. FPS-y bądź gry przygodowe."
		}
		"portuguese"
		{
			"title"		"Comando com controlos de câmara"
			"description"		"Este modelo é indicado para jogos que já têm compatibilidade nativa com comando e têm uma câmara que pode ser controlada. Por exemplo, jogos em primeira ou terceira pessoa, do género de aventura, de tiros, etc."
		}
		"romanian"
		{
			"title"		"Gamepad cu controale pentru cameră"
			"description"		"Acest șablon este pentru majoritatea jocurilor care au deja suport pentru gamepad implementat și au o cameră controlată din perspectivă first sau third person. FPS sau jocuri de aventură third person, etc."
		}
		"russian"
		{
			"title"		"Геймпад с управлением камерой"
			"description"		"Этот шаблон предназначен для большинства игр от первого или третьего лица, в которых уже есть встроенная поддержка геймпада (например, для шутеров или экшенов)."
		}
		"spanish"
		{
			"title"		"Mando con controles de cámara"
			"description"		"Esta plantilla es para la mayoría de juegos que ya incluyen de serie compatibilidad con mando y disponen de cámara controlada en primera o tercera persona: FPS, juegos de aventura en tercera persona, etc."
		}
		"swedish"
		{
			"title"		"Gamepad med kamerakontroller"
			"description"		"Denna mall är för de flesta spel som redan har inbyggt stöd för spelkontroller och har en kamera som styrs i första- eller tredjeperson. FPS eller äventyrsspel etc."
		}
		"schinese"
		{
			"title"		"支持视角控制的手柄"
			"description"		"该模板适用于已内置手柄支持，并且拥有第一或第三人称控制视角的大多数游戏。包括 FPS 或第三人称冒险游戏等。"
		}
		"thai"
		{
			"title"		"เกมแพดพร้อมการควบคุมมุมกล้อง"
			"description"		"แม่แบบนี้ใช้สำหรับเกมส่วนมากที่มีการรองรับเกมแพดมาในตัวอยู่แล้ว และมีการควบคุมมุมกล้องในมุมมองบุคคลที่หนึ่งหรือสาม เช่น เกมยิงมุมมองบุคคลที่หนึ่ง หรือเกมผจญภัยมุมมองบุคคลที่สาม ฯลฯ"
		}
		"brazilian"
		{
			"title"		"Controle com controle de câmera"
			"description"		"Este modelo é para jogos já compatíveis com controles que possuem uma câmera controlável, seja em primeira ou terceira pessoa, como jogos de tiro, aventura, etc."
		}
		"bulgarian"
		{
			"title"		"Геймпад с управление на камерата"
			"description"		"Този шаблон е за повечето игри, които вече имат вградена поддръжка на геймпад и включват управление на камерата от първо или трето лице. Екшъни от първо лице, приключенски игри от трето лице и т.н."
		}
		"greek"
		{
			"title"		"Χειριστήριο με πλήκτρα κάμερας"
			"description"		"Αυτό το πρότυπο είναι για τα περισσότερα παιχνίδια που έχουν ενσωματωμένη υποστήριξη χειριστηρίου και έχουν μια ελεγχόμενη κάμερα πρώτου ή τρίτου προσώπου. FPS ή παιχνίδια περιπέτειας τρίτου προσώπου κλπ."
		}
		"turkish"
		{
			"title"		"Kamera Kontrollü Oyun Kumandası"
			"description"		"Bu şablon hali hazırda oyun içi oyun kumandası desteği ve birincil veya üçüncü kişi kontrollü kameraya sahip oyunlar içindir. FPS veya Üçüncü Kişi Macera oyunları vb."
		}
		"ukrainian"
		{
			"title"		"Ґеймпад з елементами керування камерою"
			"description"		"Цей шаблон для більшості ігор, що вже мають вбудовану підтримку ґеймпада і у яких камера керується від першої або третьої особи. Шутери від першої особи чи пригодницькі ігри від третьої особи тощо."
		}
	}
	"group"
	{
		"id"		"0"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
			"button_a"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button A, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_b"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button B, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_x"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button X, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_y"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button Y, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"1"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button dpad_up, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button dpad_down, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button dpad_right, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button dpad_left, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"2"
		"mode"		"mouse_joystick"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"3"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_CONTROL, , "
							"binding"		"key_press M, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"4"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"controller_action hold_layer 2 1 0, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"5"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_CONTROL, , "
							"binding"		"controller_action hold_layer 3 1 0, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"6"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"10"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"11"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"12"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"14"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"15"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"16"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"18"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"21"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button DPAD_LEFT, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button DPAD_RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button DPAD_UP, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button DPAD_DOWN, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"sensitivity"		"200"
			"rotation_2"		"0"
			"friction"		"0"
			"scroll_friction"		"1"
		}
	}
	"group"
	{
		"id"		"24"
		"mode"		"radial_menu"
		"name"		"6-0"
		"description"		""
		"inputs"
		{
			"touch_menu_button_0"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 0, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_1"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 8, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_2"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 7, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_3"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 6, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_4"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 5, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button JOYSTICK_RIGHT, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"touch_menu_position_x"		"57"
			"touch_menu_position_y"		"93"
		}
	}
	"group"
	{
		"id"		"25"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"26"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"24"
		}
	}
	"group"
	{
		"id"		"27"
		"mode"		"radial_menu"
		"name"		"1-5"
		"description"		""
		"inputs"
		{
			"touch_menu_button_0"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 9, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_1"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 4, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_2"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 3, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_3"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 2, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"touch_menu_button_4"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press 1, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"touchmenu_button_fire_type"		"0"
			"touch_menu_position_x"		"40"
			"touch_menu_position_y"		"93"
		}
	}
	"group"
	{
		"id"		"30"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"33"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"34"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press BACK_TICK, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_west"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button DPAD_LEFT, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_east"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button DPAD_RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button DPAD_UP, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button DPAD_DOWN, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"sensitivity"		"200"
			"rotation_2"		"0"
			"friction"		"0"
			"mouse_smoothing"		"0"
			"scroll_friction"		"1"
		}
	}
	"group"
	{
		"id"		"37"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"40"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"43"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"46"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"gyro_ratchet_button_mask"		"32768"
		}
	}
	"group"
	{
		"id"		"48"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"49"
		"mode"		"scrollwheel"
		"name"		""
		"description"		""
		"inputs"
		{
			"scroll_clockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press DOWN_ARROW, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"scroll_counterclockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press UP_ARROW, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"scroll_angle"		"150"
			"haptic_intensity"		"1"
			"scroll_friction"		"0"
		}
	}
	"group"
	{
		"id"		"51"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"52"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"sensitivity"		"200"
		}
	}
	"group"
	{
		"id"		"54"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"56"
		"mode"		"dpad"
		"name"		""
		"description"		""
		"inputs"
		{
		}
	}
	"group"
	{
		"id"		"58"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"59"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"60"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"63"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"67"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"69"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"70"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"71"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"73"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"74"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"75"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"77"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"78"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"79"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"81"
		"mode"		"scrollwheel"
		"name"		""
		"description"		""
		"inputs"
		{
			"scroll_clockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press DOWN_ARROW, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"scroll_counterclockwise"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press UP_ARROW, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F4, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
			"scroll_angle"		"140"
		}
	}
	"group"
	{
		"id"		"83"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
			"gyro_natural_sensitivity"		"200"
		}
	}
	"group"
	{
		"id"		"85"
		"mode"		"four_buttons"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"86"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button RIGHT, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"87"
		"mode"		"trigger"
		"name"		""
		"description"		""
		"inputs"
		{
			"edge"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_button LEFT, , "
						}
						"settings"
						{
							"haptic_intensity"		"2"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"89"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
			"sensitivity"		"200"
			"friction"		"1"
		}
	}
	"group"
	{
		"id"		"90"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"91"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"92"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"94"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"95"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"96"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"97"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"99"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"100"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"101"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"102"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
			"sensitivity"		"200"
			"friction"		"1"
		}
	}
	"group"
	{
		"id"		"104"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"105"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"106"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"108"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"109"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"110"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"112"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"113"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"114"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"116"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"117"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"118"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"120"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"121"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"122"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"124"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"125"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"126"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"128"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"129"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"130"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"132"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"133"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"134"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"136"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"137"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"138"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"140"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"141"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"142"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"144"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"145"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"146"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"147"
		"mode"		"2dscroll"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F3, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_north"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_UP, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"dpad_south"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mouse_wheel SCROLL_DOWN, , "
						}
						"settings"
						{
							"haptic_intensity"		"1"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"149"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"151"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"152"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"153"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"155"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"156"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"157"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"159"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"160"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"161"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"163"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"164"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"165"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"167"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"168"
		"mode"		"absolute_mouse"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"169"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"170"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"172"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"173"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"174"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"176"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"177"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"178"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"180"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"181"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"182"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"184"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"185"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"186"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"187"
		"mode"		"joystick_move"
		"name"		""
		"description"		""
		"inputs"
		{
			"click"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_CONTROL, , "
							"binding"		"key_press M, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"189"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"190"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"191"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"193"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"194"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"195"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"27"
		}
	}
	"group"
	{
		"id"		"7"
		"mode"		"switches"
		"name"		""
		"description"		"#Description"
		"inputs"
		{
			"button_escape"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button start, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_menu"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button select, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"left_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button shoulder_left, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"right_bumper"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"xinput_button shoulder_right, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_back_left"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press LEFT_SHIFT, , "
							"binding"		"controller_action hold_layer 5 1 0, , "
						}
					}
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"mode_shift left_trackpad 49"
						}
						"settings"
						{
							"interruptable"		"0"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_back_right"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"controller_action hold_layer 4 1 0, , "
						}
						"settings"
						{
							"interruptable"		"0"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
	}
	"group"
	{
		"id"		"13"
		"mode"		"switches"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"17"
		"mode"		"switches"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"61"
		"mode"		"switches"
		"name"		""
		"description"		""
		"inputs"
		{
			"button_escape"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press SPACE, , "
						}
					}
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press FORWARD_SLASH, , "
						}
						"settings"
						{
							"delay_start"		"50"
							"interruptable"		"0"
						}
					}
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press B, , "
						}
						"settings"
						{
							"delay_start"		"100"
							"interruptable"		"0"
						}
					}
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press A, , "
						}
						"settings"
						{
							"delay_start"		"150"
							"interruptable"		"0"
						}
					}
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press N, , "
						}
						"settings"
						{
							"delay_start"		"200"
							"interruptable"		"0"
						}
					}
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press K, , "
						}
						"settings"
						{
							"delay_start"		"250"
							"interruptable"		"0"
						}
					}
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press RETURN, , "
						}
						"settings"
						{
							"delay_start"		"300"
							"interruptable"		"0"
						}
					}
				}
				"disabled_activators"
				{
				}
			}
			"button_menu"
			{
				"activators"
				{
					"Full_Press"
					{
						"bindings"
						{
							"binding"		"key_press F3, , "
						}
					}
				}
				"disabled_activators"
				{
				}
			}
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"88"
		"mode"		"switches"
		"name"		""
		"description"		""
		"inputs"
		{
		}
		"settings"
		{
			"layer"		"1"
		}
	}
	"group"
	{
		"id"		"197"
		"mode"		"reference"
		"description"		""
		"settings"
		{
			"referenced_mode"		"24"
		}
	}
	"preset"
	{
		"id"		"0"
		"name"		"Default"
		"group_source_bindings"
		{
			"7"		"switch active"
			"1"		"left_trackpad inactive"
			"21"		"left_trackpad inactive"
			"33"		"left_trackpad inactive"
			"34"		"left_trackpad active"
			"48"		"left_trackpad inactive modeshift"
			"49"		"left_trackpad inactive modeshift"
			"2"		"right_trackpad inactive"
			"6"		"right_trackpad inactive"
			"24"		"right_trackpad inactive"
			"25"		"right_trackpad inactive modeshift"
			"26"		"right_trackpad inactive"
			"30"		"right_trackpad inactive modeshift"
			"51"		"right_trackpad inactive modeshift"
			"52"		"right_trackpad inactive modeshift"
			"56"		"right_trackpad inactive modeshift"
			"149"		"right_trackpad active"
			"0"		"button_diamond active"
			"3"		"joystick active"
			"27"		"joystick inactive"
			"4"		"left_trigger active"
			"5"		"right_trigger active"
			"18"		"right_trigger inactive modeshift"
			"46"		"gyro inactive"
		}
	}
	"preset"
	{
		"id"		"1"
		"name"		"Preset_1000001"
		"group_source_bindings"
		{
			"13"		"switch active"
			"193"		"right_trackpad inactive"
			"197"		"right_trackpad active"
			"10"		"button_diamond active"
			"11"		"left_trigger active"
			"12"		"right_trigger active"
		}
	}
	"preset"
	{
		"id"		"2"
		"name"		"Preset_1000002"
		"group_source_bindings"
		{
			"17"		"switch active"
			"194"		"right_trackpad active"
			"14"		"button_diamond active"
			"15"		"left_trigger active"
			"16"		"right_trigger active"
		}
	}
	"preset"
	{
		"id"		"3"
		"name"		"Preset_1000003"
		"group_source_bindings"
		{
			"61"		"switch active"
			"81"		"left_trackpad active"
			"195"		"right_trackpad active"
			"58"		"button_diamond active"
			"187"		"joystick active"
			"59"		"left_trigger active"
			"60"		"right_trigger active"
			"83"		"gyro inactive"
		}
	}
	"preset"
	{
		"id"		"4"
		"name"		"Preset_1000004"
		"group_source_bindings"
		{
			"88"		"switch active"
			"89"		"left_trackpad inactive"
			"147"		"left_trackpad active"
			"97"		"right_trackpad inactive"
			"102"		"right_trackpad active"
			"85"		"button_diamond active"
			"86"		"left_trigger active"
			"87"		"right_trigger active"
		}
	}
	"settings"
	{
		"left_trackpad_mode"		"0"
		"right_trackpad_mode"		"0"
	}
}
